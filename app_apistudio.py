#!/usr/bin/env python3
"""
VoxChat Web Dashboard - Flask-based GUI interface
Provides a modern web interface for VoxChat with real-time monitoring and chat functionality.
"""

import asyncio
import json
import logging
import os
from datetime import datetime
from typing import Dict, Any, List

from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_cors import CORS

from main import app as voxchat_app
from voxchat.core.models import Message, MessageRole
from voxchat.core.exceptions import VoxChatError

# Initialize Flask app
flask_app = Flask(__name__, template_folder='templates')
flask_app.secret_key = os.environ.get('FLASK_SECRET_KEY', 'voxchat-dev-key-change-in-production')
CORS(flask_app)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global state
voxchat_initialized = False


def load_user_configuration():
    """Load user configuration from file if it exists."""
    try:
        config_file = '.voxchat/user_config.json'
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                saved_config = json.load(f)

            # Apply saved configuration to VoxChat
            if voxchat_app and hasattr(voxchat_app, 'config'):
                if 'temperature' in saved_config:
                    voxchat_app.config.models.temperature = saved_config['temperature']
                if 'top_p' in saved_config:
                    voxchat_app.config.models.top_p = saved_config['top_p']
                if 'max_tokens' in saved_config:
                    voxchat_app.config.models.max_tokens = saved_config['max_tokens']
                if 'frequency_penalty' in saved_config:
                    voxchat_app.config.models.frequency_penalty = saved_config['frequency_penalty']
                if 'presence_penalty' in saved_config:
                    voxchat_app.config.models.presence_penalty = saved_config['presence_penalty']

                logger.info("User configuration loaded successfully")

    except Exception as e:
        logger.warning(f"Could not load user configuration: {e}")


async def ensure_voxchat_initialized():
    """Ensure VoxChat application is initialized."""
    global voxchat_initialized
    if not voxchat_initialized:
        try:
            await voxchat_app.initialize()
            voxchat_initialized = True
            # Load user configuration after initialization
            load_user_configuration()
            logger.info("VoxChat application initialized for web interface")
        except Exception as e:
            logger.error(f"Failed to initialize VoxChat: {e}")
            raise


def run_async(coro):
    """Helper to run async functions in Flask routes."""
    import threading
    import concurrent.futures
    from flask import copy_current_request_context

    @copy_current_request_context
    def run_in_thread():
        """Run coroutine in a new thread with a fresh event loop."""
        try:
            # Create a new event loop for this thread
            new_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(new_loop)
            try:
                result = new_loop.run_until_complete(coro)
                return result
            finally:
                # Ensure the loop is properly closed
                new_loop.close()
        except Exception as e:
            logger.error(f"Error in async execution: {e}")
            raise

    # Always run in a separate thread to avoid event loop conflicts
    with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
        future = executor.submit(run_in_thread)
        try:
            return future.result(timeout=30)  # 30 second timeout
        except concurrent.futures.TimeoutError:
            raise RuntimeError("Async operation timed out after 30 seconds")


@flask_app.route('/')
def unified_interface():
    """Unified single-page interface with tabs."""
    return """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>VoxChat - AI-Powered Chat Application</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: #333;
            }

            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }

            .header {
                text-align: center;
                color: white;
                margin-bottom: 30px;
            }

            .header h1 {
                font-size: 2.5rem;
                margin-bottom: 10px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }

            .header p {
                font-size: 1.1rem;
                opacity: 0.9;
            }

            .tab-container {
                background: white;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                overflow: hidden;
            }

            .tab-nav {
                display: flex;
                background: #f8f9fa;
                border-bottom: 1px solid #dee2e6;
            }

            .tab-btn {
                flex: 1;
                padding: 15px 20px;
                background: none;
                border: none;
                cursor: pointer;
                font-size: 1rem;
                font-weight: 500;
                color: #6c757d;
                transition: all 0.3s ease;
                position: relative;
            }

            .tab-btn:hover {
                background: #e9ecef;
                color: #495057;
            }

            .tab-btn.active {
                color: #007bff;
                background: white;
            }

            .tab-btn.active::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: #007bff;
            }

            .tab-content {
                display: none;
                padding: 30px;
                min-height: 600px;
            }

            .tab-content.active {
                display: block;
            }

            /* Dashboard Styles */
            .dashboard-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }

            .metric-card {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 10px;
                border-left: 4px solid #007bff;
            }

            .metric-card h3 {
                color: #495057;
                margin-bottom: 15px;
                font-size: 1.1rem;
            }

            .metric {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
                padding: 8px 0;
                border-bottom: 1px solid #e9ecef;
            }

            .metric:last-child {
                border-bottom: none;
            }

            .metric-value {
                font-weight: 600;
                color: #007bff;
            }

            .status {
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 0.85rem;
                font-weight: 500;
            }

            .status.healthy {
                background: #d4edda;
                color: #155724;
            }

            .status.error {
                background: #f8d7da;
                color: #721c24;
            }

            /* Chat Styles */
            .chat-layout {
                display: flex;
                height: 600px;
                gap: 15px;
            }

            .conversation-sidebar {
                width: 300px;
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #dee2e6;
                display: flex;
                flex-direction: column;
            }

            .sidebar-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px;
                border-bottom: 1px solid #dee2e6;
                background: #e9ecef;
                border-radius: 8px 8px 0 0;
            }

            .sidebar-header h3 {
                margin: 0;
                font-size: 1.1rem;
                color: #495057;
            }

            .new-conversation-btn {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 50%;
                width: 30px;
                height: 30px;
                cursor: pointer;
                font-size: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .new-conversation-btn:hover {
                background: #0056b3;
            }

            .conversation-list {
                flex: 1;
                overflow-y: auto;
                padding: 10px;
            }

            .conversation-item {
                padding: 12px;
                margin-bottom: 8px;
                background: white;
                border-radius: 6px;
                border: 1px solid #e9ecef;
                cursor: pointer;
                transition: all 0.2s;
            }

            .conversation-item:hover {
                background: #e3f2fd;
                border-color: #2196f3;
            }

            .conversation-item.active {
                background: #2196f3;
                color: white;
                border-color: #1976d2;
            }

            .conversation-title {
                font-weight: 500;
                margin-bottom: 4px;
                font-size: 0.9rem;
            }

            .conversation-meta {
                font-size: 0.75rem;
                color: #6c757d;
                display: flex;
                justify-content: space-between;
            }

            .conversation-item.active .conversation-meta {
                color: rgba(255, 255, 255, 0.8);
            }

            .chat-container {
                flex: 1;
                display: flex;
                flex-direction: column;
                height: 100%;
            }

            .chat-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #dee2e6;
                margin-bottom: 15px;
            }

            .conversation-info h3 {
                margin: 0 0 5px 0;
                font-size: 1.1rem;
                color: #495057;
            }

            .model-badge {
                background: #007bff;
                color: white;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 0.75rem;
                font-weight: 500;
            }

            .model-selector-container {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .model-selector-container label {
                font-weight: 500;
                color: #495057;
                white-space: nowrap;
            }

            .model-select {
                flex: 1;
                padding: 8px 12px;
                border: 1px solid #ced4da;
                border-radius: 6px;
                background: white;
                font-size: 0.9rem;
                color: #495057;
            }

            .model-select:focus {
                outline: none;
                border-color: #007bff;
                box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
            }

            .refresh-models-btn {
                padding: 8px 12px;
                background: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 0.9rem;
                transition: background 0.3s ease;
            }

            .refresh-models-btn:hover {
                background: #5a6268;
            }

            .messages {
                flex: 1;
                border: 1px solid #dee2e6;
                border-radius: 10px;
                padding: 20px;
                overflow-y: auto;
                margin-bottom: 20px;
                background: #f8f9fa;
            }

            .message {
                margin-bottom: 15px;
                padding: 12px 16px;
                border-radius: 10px;
                max-width: 80%;
                word-wrap: break-word;
            }

            .user-message {
                background: #007bff;
                color: white;
                margin-left: auto;
                text-align: right;
            }

            .ai-message {
                background: white;
                border: 1px solid #dee2e6;
                margin-right: auto;
            }

            .message-meta {
                font-size: 0.8rem;
                opacity: 0.7;
                margin-top: 5px;
            }

            /* Markdown styling within messages */
            .message h1, .message h2, .message h3 {
                margin: 0.5em 0 0.3em 0;
                color: #333;
                font-weight: 600;
            }

            .message h1 {
                font-size: 1.4em;
                border-bottom: 2px solid #e9ecef;
                padding-bottom: 0.2em;
            }

            .message h2 {
                font-size: 1.2em;
                border-bottom: 1px solid #e9ecef;
                padding-bottom: 0.1em;
            }

            .message h3 {
                font-size: 1.1em;
            }

            .message strong {
                font-weight: 600;
                color: #2c3e50;
            }

            .message em {
                font-style: italic;
                color: #34495e;
            }

            .message code {
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 3px;
                padding: 0.1em 0.3em;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 0.9em;
                color: #e83e8c;
            }

            .message pre {
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 5px;
                padding: 1em;
                margin: 0.5em 0;
                overflow-x: auto;
            }

            .message pre code {
                background: none;
                border: none;
                padding: 0;
                color: #333;
            }

            .message ul, .message ol {
                margin: 0.5em 0;
                padding-left: 1.5em;
            }

            .message li {
                margin: 0.2em 0;
                line-height: 1.4;
            }

            .message a {
                color: #007bff;
                text-decoration: none;
            }

            .message a:hover {
                text-decoration: underline;
            }

            /* Configuration Form Styles */
            .config-description {
                color: #666;
                font-size: 0.9rem;
                margin-bottom: 20px;
                line-height: 1.4;
            }

            .config-form {
                max-width: 600px;
            }

            .form-group {
                margin-bottom: 25px;
            }

            .form-group label {
                display: block;
                margin-bottom: 8px;
                font-weight: 500;
            }

            .param-description {
                display: block;
                font-size: 0.8rem;
                color: #666;
                font-weight: normal;
                margin-top: 2px;
            }

            .slider-container {
                display: flex;
                align-items: center;
                gap: 15px;
            }

            .config-slider {
                flex: 1;
                height: 6px;
                border-radius: 3px;
                background: #ddd;
                outline: none;
                -webkit-appearance: none;
            }

            .config-slider::-webkit-slider-thumb {
                -webkit-appearance: none;
                appearance: none;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background: #007bff;
                cursor: pointer;
                border: 2px solid white;
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }

            .config-slider::-moz-range-thumb {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background: #007bff;
                cursor: pointer;
                border: 2px solid white;
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }

            .slider-value {
                min-width: 50px;
                text-align: center;
                font-weight: 600;
                color: #007bff;
                background: #f8f9fa;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 0.9rem;
            }

            .input-container {
                display: flex;
                align-items: center;
            }

            .config-input {
                width: 100%;
                padding: 10px 12px;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                font-size: 1rem;
                transition: border-color 0.2s ease;
            }

            .config-input:focus {
                outline: none;
                border-color: #007bff;
                box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
            }

            .form-actions {
                display: flex;
                gap: 10px;
                margin-top: 30px;
                flex-wrap: wrap;
            }

            .config-btn {
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-size: 0.9rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                gap: 6px;
            }

            .config-btn.primary {
                background: #007bff;
                color: white;
            }

            .config-btn.primary:hover {
                background: #0056b3;
                transform: translateY(-1px);
            }

            .config-btn.secondary {
                background: #6c757d;
                color: white;
            }

            .config-btn.secondary:hover {
                background: #545b62;
                transform: translateY(-1px);
            }

            .config-btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
            }

            .input-area {
                display: flex;
                gap: 10px;
                align-items: flex-end;
            }

            .message-input {
                flex: 1;
                padding: 12px 16px;
                border: 1px solid #dee2e6;
                border-radius: 25px;
                font-size: 1rem;
                resize: none;
                min-height: 50px;
                max-height: 120px;
            }

            .send-btn {
                padding: 12px 24px;
                background: #007bff;
                color: white;
                border: none;
                border-radius: 25px;
                cursor: pointer;
                font-size: 1rem;
                font-weight: 500;
                transition: background 0.3s ease;
                height: 50px;
            }

            .send-btn:hover {
                background: #0056b3;
            }

            .send-btn:disabled {
                background: #6c757d;
                cursor: not-allowed;
            }

            .typing-indicator {
                display: none;
                padding: 12px 16px;
                background: #e9ecef;
                border-radius: 10px;
                margin-bottom: 15px;
                max-width: 80px;
            }

            .typing-dots {
                display: flex;
                gap: 4px;
            }

            .typing-dots span {
                width: 8px;
                height: 8px;
                background: #6c757d;
                border-radius: 50%;
                animation: typing 1.4s infinite ease-in-out;
            }

            .typing-dots span:nth-child(2) {
                animation-delay: 0.2s;
            }

            .typing-dots span:nth-child(3) {
                animation-delay: 0.4s;
            }

            @keyframes typing {
                0%, 80%, 100% {
                    transform: scale(0.8);
                    opacity: 0.5;
                }
                40% {
                    transform: scale(1);
                    opacity: 1;
                }
            }

            /* Config Styles */
            .config-section {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 20px;
                border-left: 4px solid #28a745;
            }

            .config-section h3 {
                color: #495057;
                margin-bottom: 15px;
            }

            .config-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 0;
                border-bottom: 1px solid #e9ecef;
            }

            .config-item:last-child {
                border-bottom: none;
            }

            .refresh-btn {
                background: #28a745;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 1rem;
                margin-top: 20px;
            }

            .refresh-btn:hover {
                background: #218838;
            }

            @media (max-width: 768px) {
                .container {
                    padding: 10px;
                }

                .header h1 {
                    font-size: 2rem;
                }

                .dashboard-grid {
                    grid-template-columns: 1fr;
                }

                .tab-nav {
                    flex-direction: column;
                }

                .chat-layout {
                    flex-direction: column;
                    height: auto;
                }

                .conversation-sidebar {
                    width: 100%;
                    height: 200px;
                    margin-bottom: 15px;
                }

                .chat-header {
                    flex-direction: column;
                    gap: 10px;
                    align-items: stretch;
                }

                .model-selector-container {
                    justify-content: center;
                }
            }

                .message {
                    max-width: 95%;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🤖 VoxChat</h1>
                <p>AI-Powered Chat Application with Voice Processing</p>
            </div>

            <div class="tab-container">
                <div class="tab-nav">
                    <button class="tab-btn active" onclick="switchTab('dashboard', this)">📊 Dashboard</button>
                    <button class="tab-btn" onclick="switchTab('chat', this)">💬 Chat</button>
                    <button class="tab-btn" onclick="switchTab('config', this)">⚙️ Configuration</button>
                </div>

                <!-- Dashboard Tab -->
                <div id="dashboard" class="tab-content active">
                    <div class="dashboard-grid">
                        <div class="metric-card">
                            <h3>📊 System Status</h3>
                            <div class="metric">
                                <span>Status:</span>
                                <span class="status healthy" id="system-status">Loading...</span>
                            </div>
                            <div class="metric">
                                <span>Uptime:</span>
                                <span class="metric-value" id="uptime">Loading...</span>
                            </div>
                            <div class="metric">
                                <span>Requests Processed:</span>
                                <span class="metric-value" id="requests-processed">Loading...</span>
                            </div>
                            <div class="metric">
                                <span>Total Tokens:</span>
                                <span class="metric-value" id="total-tokens">Loading...</span>
                            </div>
                        </div>

                        <div class="metric-card">
                            <h3>🤖 API Status</h3>
                            <div class="metric">
                                <span>OpenRouter:</span>
                                <span class="status healthy">Connected</span>
                            </div>
                            <div class="metric">
                                <span>Anthropic:</span>
                                <span class="status error">Not Configured</span>
                            </div>
                            <div class="metric">
                                <span>OpenAI:</span>
                                <span class="status error">Not Configured</span>
                            </div>
                        </div>

                        <div class="metric-card">
                            <h3>💰 Cost Analytics</h3>
                            <div class="metric">
                                <span>Estimated Cost:</span>
                                <span class="metric-value" id="estimated-cost">Loading...</span>
                            </div>
                            <div class="metric">
                                <span>Conversations:</span>
                                <span class="metric-value" id="conversations-count">Loading...</span>
                            </div>
                            <div class="metric">
                                <span>Avg Tokens/Conv:</span>
                                <span class="metric-value" id="avg-tokens">Loading...</span>
                            </div>
                        </div>

                        <div class="metric-card">
                            <h3>🔧 Services Status</h3>
                            <div id="services-status">Loading...</div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <h3>💬 Recent Conversations</h3>
                        <div id="recent-conversations">Loading...</div>
                    </div>
                </div>

                <!-- Chat Tab -->
                <div id="chat" class="tab-content">
                    <div class="chat-layout">
                        <!-- Conversation Sidebar -->
                        <div class="conversation-sidebar">
                            <div class="sidebar-header">
                                <h3>💬 Conversations</h3>
                                <button onclick="createNewConversation()" class="new-conversation-btn" title="New Conversation">➕</button>
                            </div>
                            <div class="conversation-list" id="conversation-list">
                                <div class="loading">Loading conversations...</div>
                            </div>
                        </div>

                        <!-- Chat Area -->
                        <div class="chat-container">
                            <!-- Chat Header -->
                            <div class="chat-header">
                                <div class="conversation-info">
                                    <h3 id="current-conversation-title">New Conversation</h3>
                                    <span id="current-conversation-model" class="model-badge">anthropic/claude-3-haiku</span>
                                </div>
                                <!-- Model Selection -->
                                <div class="model-selector-container">
                                    <label for="model-select">🤖 AI Model:</label>
                                    <select id="model-select" class="model-select">
                                        <option value="anthropic/claude-3-haiku">Claude 3 Haiku (Default)</option>
                                        <option value="loading">Loading models...</option>
                                    </select>
                                    <button onclick="refreshModels()" class="refresh-models-btn" title="Refresh Models">🔄</button>
                                </div>
                            </div>

                            <div id="messages" class="messages">
                                <div class="ai-message">
                                    <div>👋 Hello! I'm your AI assistant. How can I help you today?</div>
                                    <div class="message-meta">AI Assistant</div>
                                </div>
                            </div>

                        <div class="typing-indicator" id="typing-indicator">
                            <div class="typing-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>

                        <div class="input-area">
                            <textarea
                                id="messageInput"
                                class="message-input"
                                placeholder="Type your message here..."
                                onkeypress="handleKeyPress(event)"
                                rows="1"
                            ></textarea>
                            <button onclick="sendMessage()" class="send-btn" id="send-btn">Send</button>
                        </div>
                    </div>
                </div>

                <!-- Configuration Tab -->
                <div id="config" class="tab-content">
                    <div style="background: red; color: white; padding: 20px; margin: 20px; font-size: 18px; font-weight: bold; text-align: center; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.3);">
                        🎉 SUCCESS! Configuration tab is working! 🎉<br>
                        <small style="font-size: 14px; font-weight: normal;">If you can see this message, the tab switching is working correctly.</small>
                    </div>
                    <div class="config-section">
                        <h3>🔑 API Configuration</h3>
                        <div class="config-item">
                            <span>OpenRouter API:</span>
                            <span class="status healthy">✅ Configured</span>
                        </div>
                        <div class="config-item">
                            <span>API Keys Count:</span>
                            <span class="metric-value">1</span>
                        </div>
                        <div class="config-item">
                            <span>Base URL:</span>
                            <span>https://openrouter.ai/api/v1</span>
                        </div>
                    </div>

                    <div class="config-section">
                        <h3>🤖 Model Parameters</h3>
                        <p class="config-description">Adjust AI model parameters to control response behavior. Changes apply to new conversations.</p>

                        <div class="config-form">
                            <div class="form-group">
                                <label for="temperature-slider">
                                    <strong>Temperature</strong>
                                    <span class="param-description">Controls randomness (0.0 = deterministic, 2.0 = very creative)</span>
                                </label>
                                <div class="slider-container">
                                    <input type="range" id="temperature-slider" min="0" max="2" step="0.1" value="0.7" class="config-slider">
                                    <span class="slider-value" id="temperature-value">0.7</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="top-p-slider">
                                    <strong>Top-p (Nucleus Sampling)</strong>
                                    <span class="param-description">Controls diversity by limiting token choices (0.1 = focused, 1.0 = diverse)</span>
                                </label>
                                <div class="slider-container">
                                    <input type="range" id="top-p-slider" min="0.1" max="1" step="0.05" value="0.9" class="config-slider">
                                    <span class="slider-value" id="top-p-value">0.9</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="max-tokens-input">
                                    <strong>Max Tokens</strong>
                                    <span class="param-description">Maximum length of AI response (1-128000 tokens)</span>
                                </label>
                                <div class="input-container">
                                    <input type="number" id="max-tokens-input" min="1" max="128000" value="4096" class="config-input">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="frequency-penalty-slider">
                                    <strong>Frequency Penalty</strong>
                                    <span class="param-description">Reduces repetition of frequent tokens (-2.0 to 2.0)</span>
                                </label>
                                <div class="slider-container">
                                    <input type="range" id="frequency-penalty-slider" min="-2" max="2" step="0.1" value="0" class="config-slider">
                                    <span class="slider-value" id="frequency-penalty-value">0.0</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="presence-penalty-slider">
                                    <strong>Presence Penalty</strong>
                                    <span class="param-description">Encourages new topics by penalizing repeated tokens (-2.0 to 2.0)</span>
                                </label>
                                <div class="slider-container">
                                    <input type="range" id="presence-penalty-slider" min="-2" max="2" step="0.1" value="0" class="config-slider">
                                    <span class="slider-value" id="presence-penalty-value">0.0</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="stop-sequences-input">
                                    <strong>Stop Sequences</strong>
                                    <span class="param-description">Text sequences that stop generation (comma-separated, max 4)</span>
                                </label>
                                <div class="input-container">
                                    <input type="text" id="stop-sequences-input" placeholder="e.g., \\n\\n, END, STOP" class="config-input">
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="config-btn primary" onclick="saveConfiguration()">💾 Save Configuration</button>
                                <button type="button" class="config-btn secondary" onclick="resetConfiguration()">🔄 Reset to Defaults</button>
                                <button type="button" class="config-btn secondary" onclick="loadConfiguration()">📥 Load Current</button>
                            </div>
                        </div>
                    </div>

                    <div class="config-section">
                        <h3>📁 File Processing</h3>
                        <div class="config-item">
                            <span>Supported Formats:</span>
                            <span>PDF, DOCX, TXT, Images</span>
                        </div>
                        <div class="config-item">
                            <span>Max File Size:</span>
                            <span>10MB</span>
                        </div>
                        <div class="config-item">
                            <span>PDF Support:</span>
                            <span class="status healthy">✅ Available</span>
                        </div>
                        <div class="config-item">
                            <span>DOCX Support:</span>
                            <span class="status healthy">✅ Available</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // Tab switching functionality
            function switchTab(tabName, clickedButton) {
                console.log(`Switching to tab: ${tabName}`);

                // Hide all tab contents
                document.querySelectorAll('.tab-content').forEach(tab => {
                    tab.classList.remove('active');
                    console.log(`Hiding tab: ${tab.id}`);
                });

                // Remove active class from all tab buttons
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.classList.remove('active');
                });

                // Show selected tab content
                const targetTab = document.getElementById(tabName);
                if (targetTab) {
                    targetTab.classList.add('active');
                    console.log(`Showing tab: ${tabName}, element found:`, targetTab);
                } else {
                    console.error(`Tab element not found: ${tabName}`);
                }

                // Add active class to clicked button
                if (clickedButton) {
                    clickedButton.classList.add('active');
                } else {
                    // Fallback: find the button by tab name
                    const buttons = document.querySelectorAll('.tab-btn');
                    buttons.forEach(btn => {
                        if (btn.textContent.includes(tabName === 'dashboard' ? 'Dashboard' :
                                                   tabName === 'chat' ? 'Chat' : 'Configuration')) {
                            btn.classList.add('active');
                        }
                    });
                }

                // Load data for specific tabs
                if (tabName === 'dashboard') {
                    loadDashboardData();
                } else if (tabName === 'chat') {
                    // Load models and conversations when chat tab is accessed
                    const modelSelect = document.getElementById('model-select');
                    if (modelSelect.options.length <= 2) { // Only default and loading options
                        loadModels();
                    }
                    loadConversations();

                    // Create a new conversation if none exists
                    if (!currentConversationId) {
                        createNewConversation();
                    }
                } else if (tabName === 'config') {
                    console.log('Loading configuration tab...');
                    // Load current configuration when switching to config tab
                    setTimeout(() => {
                        loadConfiguration();
                    }, 100);
                }
            }

            // Dashboard functionality
            async function loadDashboardData() {
                try {
                    const response = await fetch('/api/status');
                    const result = await response.json();

                    if (result.status === 'success') {
                        const data = result.data;

                        // Update system status
                        document.getElementById('system-status').textContent = data.health.status || 'Unknown';
                        document.getElementById('uptime').textContent = Math.floor(data.performance.uptime_seconds || 0) + 's';
                        document.getElementById('requests-processed').textContent = data.performance.requests_processed || 0;
                        document.getElementById('total-tokens').textContent = data.performance.total_tokens || 0;

                        // Update services status
                        const servicesDiv = document.getElementById('services-status');
                        const services = data.performance.services_status || {};
                        servicesDiv.innerHTML = '';

                        Object.entries(services).forEach(([service, status]) => {
                            const serviceDiv = document.createElement('div');
                            serviceDiv.className = 'metric';
                            serviceDiv.innerHTML = `
                                <span>${service.charAt(0).toUpperCase() + service.slice(1)}:</span>
                                <span class="status ${status ? 'healthy' : 'error'}">${status ? 'Running' : 'Stopped'}</span>
                            `;
                            servicesDiv.appendChild(serviceDiv);
                        });
                    }

                    // Load conversations
                    const convResponse = await fetch('/api/conversations');
                    const convResult = await convResponse.json();

                    if (convResult.status === 'success') {
                        const conversations = convResult.data;
                        document.getElementById('conversations-count').textContent = conversations.length;
                        document.getElementById('estimated-cost').textContent = '$' + (conversations.length * 0.001).toFixed(4);
                        document.getElementById('avg-tokens').textContent = conversations.length > 0 ?
                            Math.floor(conversations.reduce((sum, conv) => sum + (conv.total_tokens || 0), 0) / conversations.length) : 0;

                        // Update recent conversations
                        const recentDiv = document.getElementById('recent-conversations');
                        recentDiv.innerHTML = '';

                        if (conversations.length === 0) {
                            recentDiv.innerHTML = '<p style="color: #666; font-style: italic;">No conversations yet</p>';
                        } else {
                            conversations.slice(0, 5).forEach(conv => {
                                const convDiv = document.createElement('div');
                                convDiv.className = 'metric';
                                convDiv.innerHTML = `
                                    <div>
                                        <strong>${conv.title}</strong><br>
                                        <small>Messages: ${conv.message_count} | Tokens: ${conv.total_tokens || 0} | Created: ${new Date(conv.created_at).toLocaleDateString()}</small>
                                    </div>
                                `;
                                recentDiv.appendChild(convDiv);
                            });
                        }
                    }

                } catch (error) {
                    console.error('Error loading dashboard data:', error);
                }
            }

            // Simple Markdown rendering function
            function renderMarkdown(text) {
                // Escape HTML to prevent XSS attacks
                const escapeHtml = (unsafe) => {
                    return unsafe
                        .replace(/&/g, "&amp;")
                        .replace(/</g, "&lt;")
                        .replace(/>/g, "&gt;")
                        .replace(/"/g, "&quot;")
                        .replace(/'/g, "&#039;");
                };

                // First escape HTML, then apply markdown transformations
                let html = escapeHtml(text);

                // Headers (### ## #)
                html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
                html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
                html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');

                // Bold text (**text**) - using string methods to avoid regex escaping issues
                while (html.includes('**')) {
                    const start = html.indexOf('**');
                    const end = html.indexOf('**', start + 2);
                    if (end !== -1) {
                        const before = html.substring(0, start);
                        const content = html.substring(start + 2, end);
                        const after = html.substring(end + 2);
                        html = before + '<strong>' + content + '</strong>' + after;
                    } else {
                        break;
                    }
                }

                // Code blocks (```code```)
                while (html.includes('```')) {
                    const start = html.indexOf('```');
                    const end = html.indexOf('```', start + 3);
                    if (end !== -1) {
                        const before = html.substring(0, start);
                        const content = html.substring(start + 3, end);
                        const after = html.substring(end + 3);
                        html = before + '<pre><code>' + content + '</code></pre>' + after;
                    } else {
                        break;
                    }
                }

                // Inline code (`code`)
                while (html.includes('`')) {
                    const start = html.indexOf('`');
                    const end = html.indexOf('`', start + 1);
                    if (end !== -1) {
                        const before = html.substring(0, start);
                        const content = html.substring(start + 1, end);
                        const after = html.substring(end + 1);
                        html = before + '<code>' + content + '</code>' + after;
                    } else {
                        break;
                    }
                }

                // Process bullet points and numbered lists
                const newlineChar = String.fromCharCode(10); // \\n character
                const lines = html.split(newlineChar);
                let inList = false;
                let listType = null;
                const processedLines = [];

                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i];
                    const trimmed = line.trim();

                    // Check for bullet points
                    if (trimmed.startsWith('* ')) {
                        if (!inList || listType !== 'ul') {
                            if (inList) processedLines.push('</' + listType + '>');
                            processedLines.push('<ul>');
                            inList = true;
                            listType = 'ul';
                        }
                        processedLines.push('<li>' + trimmed.substring(2) + '</li>');
                    }
                    // Check for numbered lists - using string methods instead of regex
                    else if (trimmed.length > 2 && trimmed.charAt(0) >= '0' && trimmed.charAt(0) <= '9') {
                        let dotIndex = -1;
                        for (let j = 1; j < trimmed.length; j++) {
                            if (trimmed.charAt(j) === '.' && trimmed.charAt(j + 1) === ' ') {
                                dotIndex = j;
                                break;
                            }
                            if (trimmed.charAt(j) < '0' || trimmed.charAt(j) > '9') {
                                break;
                            }
                        }

                        if (dotIndex > 0) {
                            if (!inList || listType !== 'ol') {
                                if (inList) processedLines.push('</' + listType + '>');
                                processedLines.push('<ol>');
                                inList = true;
                                listType = 'ol';
                            }
                            const content = trimmed.substring(dotIndex + 2);
                            processedLines.push('<li>' + content + '</li>');
                        } else {
                            if (inList && trimmed === '') {
                                processedLines.push('');
                            } else if (inList && trimmed !== '') {
                                processedLines.push('</' + listType + '>');
                                inList = false;
                                listType = null;
                                processedLines.push(line);
                            } else {
                                processedLines.push(line);
                            }
                        }
                    } else {
                        if (inList && trimmed === '') {
                            // Empty line continues the list
                            processedLines.push('');
                        } else if (inList && trimmed !== '') {
                            // Non-list line ends the list
                            processedLines.push('</' + listType + '>');
                            inList = false;
                            listType = null;
                            processedLines.push(line);
                        } else {
                            processedLines.push(line);
                        }
                    }
                }

                // Close any remaining list
                if (inList) {
                    processedLines.push('</' + listType + '>');
                }

                html = processedLines.join(newlineChar);

                // Convert line breaks to <br> tags
                const newlineRegex = new RegExp(newlineChar, 'g');
                html = html.replace(newlineRegex, '<br>');

                return html;
            }

            // Chat functionality
            function addMessage(content, isUser, isError = false) {
                const messages = document.getElementById('messages');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message ' + (isUser ? 'user-message' : 'ai-message');

                const contentDiv = document.createElement('div');

                // Render markdown for AI messages, plain text for user messages and errors
                if (!isUser && !isError) {
                    contentDiv.innerHTML = renderMarkdown(content);
                } else {
                    contentDiv.textContent = content;
                }

                messageDiv.appendChild(contentDiv);

                const metaDiv = document.createElement('div');
                metaDiv.className = 'message-meta';
                metaDiv.textContent = isUser ? 'You' : (isError ? 'Error' : 'AI Assistant');
                messageDiv.appendChild(metaDiv);

                if (isError) {
                    messageDiv.style.borderColor = '#dc3545';
                    messageDiv.style.backgroundColor = '#f8d7da';
                }

                messages.appendChild(messageDiv);
                messages.scrollTop = messages.scrollHeight;
            }

            function showTypingIndicator() {
                document.getElementById('typing-indicator').style.display = 'block';
                const messages = document.getElementById('messages');
                messages.scrollTop = messages.scrollHeight;
            }

            function hideTypingIndicator() {
                document.getElementById('typing-indicator').style.display = 'none';
            }

            function handleKeyPress(event) {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    sendMessage();
                }
            }

            async function sendMessage() {
                const input = document.getElementById('messageInput');
                const sendBtn = document.getElementById('send-btn');
                const modelSelect = document.getElementById('model-select');
                const message = input.value.trim();

                if (!message) return;

                // Get selected model
                const selectedModel = modelSelect.value;
                if (selectedModel === 'loading') {
                    addMessage('Please wait for models to load before sending a message.', false, true);
                    return;
                }

                // Disable input and button
                input.disabled = true;
                sendBtn.disabled = true;
                sendBtn.textContent = 'Sending...';

                // Add user message
                addMessage(message, true);
                input.value = '';

                // Show typing indicator
                showTypingIndicator();

                try {
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            message: message,
                            model: selectedModel,
                            conversation_id: currentConversationId
                        })
                    });

                    const result = await response.json();
                    hideTypingIndicator();

                    if (result.status === 'success' || result.status === 'partial_success') {
                        const aiResponse = result.data.ai_response;
                        addMessage(aiResponse.content, false, aiResponse.error || false);

                        if (result.data.total_tokens) {
                            console.log(`Tokens used: ${result.data.total_tokens} (Model: ${result.data.model_used})`);
                        }

                        // Update current conversation ID if it was created
                        if (result.data.conversation_id && !currentConversationId) {
                            currentConversationId = result.data.conversation_id;
                        }

                        // Refresh conversation list to show updated message count
                        loadConversations();
                    } else {
                        addMessage('Error: ' + result.message, false, true);
                    }
                } catch (error) {
                    hideTypingIndicator();
                    addMessage('Network error: ' + error.message, false, true);
                }

                // Re-enable input and button
                input.disabled = false;
                sendBtn.disabled = false;
                sendBtn.textContent = 'Send';
                input.focus();
            }

            // Model management functions
            function saveSelectedModel(modelId) {
                localStorage.setItem('voxchat_selected_model', modelId);
                console.log(`Saved selected model: ${modelId}`);
            }

            function getSelectedModel() {
                return localStorage.getItem('voxchat_selected_model') || 'anthropic/claude-3-haiku';
            }

            async function loadModels() {
                try {
                    const response = await fetch('/api/models');
                    const result = await response.json();

                    if (result.status === 'success') {
                        const modelSelect = document.getElementById('model-select');
                        const currentValue = modelSelect.value;
                        const savedModel = getSelectedModel();

                        // Clear existing options except default
                        modelSelect.innerHTML = '<option value="anthropic/claude-3-haiku">Claude 3 Haiku (Default)</option>';

                        // Add popular models first
                        const popularModels = [
                            'anthropic/claude-3-sonnet',
                            'anthropic/claude-3-opus',
                            'openai/gpt-4',
                            'openai/gpt-3.5-turbo',
                            'google/gemini-pro',
                            'deepseek/deepseek-chat',
                            'tngtech/deepseek-r1t2-chimera:free'
                        ];

                        const models = result.data;
                        const addedModels = new Set(['anthropic/claude-3-haiku']);

                        // Add popular models
                        popularModels.forEach(modelId => {
                            const model = models.find(m => m.id === modelId);
                            if (model && !addedModels.has(model.id)) {
                                const option = document.createElement('option');
                                option.value = model.id;
                                // Add "FREE" indicator for free models
                                const isFree = model.id.includes(':free');
                                option.textContent = isFree ? `${model.name} (FREE)` : model.name;
                                modelSelect.appendChild(option);
                                addedModels.add(model.id);
                            }
                        });

                        // Add separator
                        const separator = document.createElement('option');
                        separator.disabled = true;
                        separator.textContent = '── Other Models ──';
                        modelSelect.appendChild(separator);

                        // Add remaining models
                        models.forEach(model => {
                            if (!addedModels.has(model.id)) {
                                const option = document.createElement('option');
                                option.value = model.id;
                                // Add "FREE" indicator for free models
                                const isFree = model.id.includes(':free');
                                option.textContent = isFree ? `${model.name} (FREE)` : model.name;
                                modelSelect.appendChild(option);
                            }
                        });

                        // Restore previous selection - prioritize saved model over current value
                        const modelToRestore = (currentValue && currentValue !== 'loading') ? currentValue : savedModel;

                        // Check if the model to restore exists in the dropdown
                        const modelExists = Array.from(modelSelect.options).some(option => option.value === modelToRestore);

                        if (modelExists) {
                            modelSelect.value = modelToRestore;
                            console.log(`Restored model selection: ${modelToRestore}`);
                        } else {
                            console.log(`Model ${modelToRestore} not found, using default`);
                        }

                        console.log(`Loaded ${models.length} models`);
                    } else {
                        console.error('Failed to load models:', result.message);
                    }
                } catch (error) {
                    console.error('Error loading models:', error);
                }
            }

            function refreshModels() {
                const modelSelect = document.getElementById('model-select');
                modelSelect.innerHTML = '<option value="loading">Loading models...</option>';
                loadModels();
            }

            function refreshDashboard() {
                loadDashboardData();
            }

            // Conversation management
            let currentConversationId = null;

            async function loadConversations() {
                try {
                    const response = await fetch('/api/conversations');
                    const result = await response.json();

                    if (result.status === 'success') {
                        const conversationList = document.getElementById('conversation-list');
                        conversationList.innerHTML = '';

                        if (result.data.length === 0) {
                            conversationList.innerHTML = '<div class="no-conversations">No conversations yet. Click + to start!</div>';
                            return;
                        }

                        result.data.forEach(conv => {
                            const convItem = document.createElement('div');
                            convItem.className = 'conversation-item';
                            convItem.dataset.conversationId = conv.id;

                            if (conv.id === currentConversationId) {
                                convItem.classList.add('active');
                            }

                            convItem.innerHTML = `
                                <div class="conversation-title">${conv.title}</div>
                                <div class="conversation-meta">
                                    <span>${conv.message_count} msgs</span>
                                    <span>${conv.model_name.split('/').pop()}</span>
                                </div>
                            `;

                            convItem.onclick = () => switchToConversation(conv.id);
                            conversationList.appendChild(convItem);
                        });
                    }
                } catch (error) {
                    console.error('Error loading conversations:', error);
                }
            }

            async function switchToConversation(conversationId) {
                try {
                    const response = await fetch(`/api/conversations/${conversationId}/switch`, {
                        method: 'POST'
                    });
                    const result = await response.json();

                    if (result.status === 'success') {
                        currentConversationId = conversationId;
                        const conv = result.data;

                        // Update UI
                        document.getElementById('current-conversation-title').textContent = conv.title;
                        document.getElementById('current-conversation-model').textContent = conv.model_name;
                        document.getElementById('model-select').value = conv.model_name;

                        // Clear and load messages
                        const messagesContainer = document.getElementById('messages');
                        messagesContainer.innerHTML = '';

                        if (conv.messages.length === 0) {
                            messagesContainer.innerHTML = `
                                <div class="ai-message">
                                    <div>👋 Hello! I'm your AI assistant. How can I help you today?</div>
                                    <div class="message-meta">AI Assistant</div>
                                </div>
                            `;
                        } else {
                            conv.messages.forEach(msg => {
                                addMessage(msg.content, msg.role === 'user', false, false);
                            });
                        }

                        // Update conversation list active state
                        document.querySelectorAll('.conversation-item').forEach(item => {
                            item.classList.remove('active');
                        });
                        document.querySelector(`[data-conversation-id="${conversationId}"]`).classList.add('active');

                        console.log(`Switched to conversation: ${conv.title}`);
                    }
                } catch (error) {
                    console.error('Error switching conversation:', error);
                }
            }

            async function createNewConversation() {
                try {
                    const selectedModel = document.getElementById('model-select').value;
                    const response = await fetch('/api/conversations/new', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            title: 'New Conversation',
                            model: selectedModel
                        })
                    });
                    const result = await response.json();

                    if (result.status === 'success') {
                        await loadConversations();
                        await switchToConversation(result.data.id);
                    }
                } catch (error) {
                    console.error('Error creating conversation:', error);
                }
            }

            // Auto-resize textarea
            document.getElementById('messageInput').addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            // Toast notification function for model changes
            function showModelChangeNotification(modelId) {
                // Get model display name from the select option
                const modelSelect = document.getElementById('model-select');
                const selectedOption = modelSelect.querySelector(`option[value="${modelId}"]`);
                const modelName = selectedOption ? selectedOption.textContent : modelId;

                // Create toast element
                const toast = document.createElement('div');
                toast.className = 'model-change-toast';
                toast.innerHTML = `
                    <div class="toast-content">
                        <span class="toast-icon">🤖</span>
                        <span class="toast-message">Model changed to: ${modelName}</span>
                    </div>
                `;

                // Add toast styles
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #4CAF50;
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 10000;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-size: 14px;
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                `;

                document.body.appendChild(toast);

                // Animate in
                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translateX(0)';
                }, 10);

                // Remove after 3 seconds
                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.parentNode.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            }

            // Configuration Management
            let currentConfig = {
                temperature: 0.7,
                top_p: 0.9,
                max_tokens: 4096,
                frequency_penalty: 0.0,
                presence_penalty: 0.0,
                stop_sequences: []
            };

            // Initialize configuration sliders and inputs
            function initializeConfigurationControls() {
                // Temperature slider
                const tempSlider = document.getElementById('temperature-slider');
                const tempValue = document.getElementById('temperature-value');
                if (tempSlider && tempValue) {
                    tempSlider.addEventListener('input', function() {
                        tempValue.textContent = parseFloat(this.value).toFixed(1);
                        currentConfig.temperature = parseFloat(this.value);
                    });
                }

                // Top-p slider
                const topPSlider = document.getElementById('top-p-slider');
                const topPValue = document.getElementById('top-p-value');
                if (topPSlider && topPValue) {
                    topPSlider.addEventListener('input', function() {
                        topPValue.textContent = parseFloat(this.value).toFixed(2);
                        currentConfig.top_p = parseFloat(this.value);
                    });
                }

                // Max tokens input
                const maxTokensInput = document.getElementById('max-tokens-input');
                if (maxTokensInput) {
                    maxTokensInput.addEventListener('input', function() {
                        let value = parseInt(this.value);
                        if (value < 1) value = 1;
                        if (value > 128000) value = 128000;
                        this.value = value;
                        currentConfig.max_tokens = value;
                    });
                }

                // Frequency penalty slider
                const freqPenaltySlider = document.getElementById('frequency-penalty-slider');
                const freqPenaltyValue = document.getElementById('frequency-penalty-value');
                if (freqPenaltySlider && freqPenaltyValue) {
                    freqPenaltySlider.addEventListener('input', function() {
                        freqPenaltyValue.textContent = parseFloat(this.value).toFixed(1);
                        currentConfig.frequency_penalty = parseFloat(this.value);
                    });
                }

                // Presence penalty slider
                const presPenaltySlider = document.getElementById('presence-penalty-slider');
                const presPenaltyValue = document.getElementById('presence-penalty-value');
                if (presPenaltySlider && presPenaltyValue) {
                    presPenaltySlider.addEventListener('input', function() {
                        presPenaltyValue.textContent = parseFloat(this.value).toFixed(1);
                        currentConfig.presence_penalty = parseFloat(this.value);
                    });
                }

                // Stop sequences input
                const stopSeqInput = document.getElementById('stop-sequences-input');
                if (stopSeqInput) {
                    stopSeqInput.addEventListener('input', function() {
                        const sequences = this.value.split(',').map(s => s.trim()).filter(s => s.length > 0);
                        currentConfig.stop_sequences = sequences.slice(0, 4); // Max 4 sequences
                    });
                }
            }

            // Load current configuration from server
            async function loadConfiguration() {
                console.log('loadConfiguration() called');
                try {
                    console.log('Fetching configuration from /api/config');
                    const response = await fetch('/api/config');
                    console.log('Configuration response:', response.status, response.ok);
                    if (response.ok) {
                        const config = await response.json();
                        console.log('Configuration data received:', config);
                        updateConfigurationUI(config.data || config);
                        showConfigToast('Configuration loaded successfully', 'success');
                    } else {
                        console.error('Failed to load configuration, status:', response.status);
                        showConfigToast('Failed to load configuration', 'error');
                    }
                } catch (error) {
                    console.error('Error loading configuration:', error);
                    showConfigToast('Error loading configuration', 'error');
                }
            }

            // Update UI with configuration values
            function updateConfigurationUI(config) {
                console.log('updateConfigurationUI() called with:', config);
                currentConfig = { ...config };

                // Update sliders and inputs
                const tempSlider = document.getElementById('temperature-slider');
                const tempValue = document.getElementById('temperature-value');
                console.log('Temperature slider element:', tempSlider);
                console.log('Temperature value element:', tempValue);
                if (tempSlider && tempValue) {
                    tempSlider.value = config.temperature || 0.7;
                    tempValue.textContent = (config.temperature || 0.7).toFixed(1);
                    console.log('Updated temperature to:', config.temperature || 0.7);
                } else {
                    console.error('Temperature slider or value element not found');
                }

                const topPSlider = document.getElementById('top-p-slider');
                const topPValue = document.getElementById('top-p-value');
                if (topPSlider && topPValue) {
                    topPSlider.value = config.top_p || 0.9;
                    topPValue.textContent = (config.top_p || 0.9).toFixed(2);
                }

                const maxTokensInput = document.getElementById('max-tokens-input');
                if (maxTokensInput) {
                    maxTokensInput.value = config.max_tokens || 4096;
                }

                const freqPenaltySlider = document.getElementById('frequency-penalty-slider');
                const freqPenaltyValue = document.getElementById('frequency-penalty-value');
                if (freqPenaltySlider && freqPenaltyValue) {
                    freqPenaltySlider.value = config.frequency_penalty || 0.0;
                    freqPenaltyValue.textContent = (config.frequency_penalty || 0.0).toFixed(1);
                }

                const presPenaltySlider = document.getElementById('presence-penalty-slider');
                const presPenaltyValue = document.getElementById('presence-penalty-value');
                if (presPenaltySlider && presPenaltyValue) {
                    presPenaltySlider.value = config.presence_penalty || 0.0;
                    presPenaltyValue.textContent = (config.presence_penalty || 0.0).toFixed(1);
                }

                const stopSeqInput = document.getElementById('stop-sequences-input');
                if (stopSeqInput) {
                    stopSeqInput.value = (config.stop_sequences || []).join(', ');
                }
            }

            // Save configuration to server
            async function saveConfiguration() {
                try {
                    const saveBtn = document.querySelector('.config-btn.primary');
                    if (saveBtn) {
                        saveBtn.disabled = true;
                        saveBtn.textContent = '💾 Saving...';
                    }

                    const response = await fetch('/api/config', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(currentConfig)
                    });

                    if (response.ok) {
                        showConfigToast('Configuration saved successfully! Changes will apply to new conversations.', 'success');
                    } else {
                        const error = await response.json();
                        showConfigToast(`Failed to save configuration: ${error.message || 'Unknown error'}`, 'error');
                    }
                } catch (error) {
                    console.error('Error saving configuration:', error);
                    showConfigToast('Error saving configuration', 'error');
                } finally {
                    const saveBtn = document.querySelector('.config-btn.primary');
                    if (saveBtn) {
                        saveBtn.disabled = false;
                        saveBtn.textContent = '💾 Save Configuration';
                    }
                }
            }

            // Reset configuration to defaults
            function resetConfiguration() {
                const defaultConfig = {
                    temperature: 0.7,
                    top_p: 0.9,
                    max_tokens: 4096,
                    frequency_penalty: 0.0,
                    presence_penalty: 0.0,
                    stop_sequences: []
                };

                updateConfigurationUI(defaultConfig);
                showConfigToast('Configuration reset to defaults', 'info');
            }

            // Show toast notification for configuration
            function showConfigToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `config-toast config-toast-${type}`;
                toast.textContent = message;

                // Toast styles
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 12px 20px;
                    border-radius: 8px;
                    color: white;
                    font-weight: 500;
                    z-index: 10000;
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                    max-width: 400px;
                    word-wrap: break-word;
                `;

                // Set background color based on type
                switch (type) {
                    case 'success':
                        toast.style.background = '#28a745';
                        break;
                    case 'error':
                        toast.style.background = '#dc3545';
                        break;
                    case 'info':
                        toast.style.background = '#17a2b8';
                        break;
                    default:
                        toast.style.background = '#6c757d';
                }

                document.body.appendChild(toast);

                // Animate in
                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translateX(0)';
                }, 10);

                // Remove after 4 seconds
                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.parentNode.removeChild(toast);
                        }
                    }, 300);
                }, 4000);
            }

            // Test function for debugging tab switching
            window.testConfigTab = function() {
                console.log('Testing configuration tab...');
                const configTab = document.getElementById('config');
                console.log('Config tab element:', configTab);
                console.log('Config tab classes:', configTab ? configTab.className : 'NOT FOUND');
                console.log('Config tab display style:', configTab ? window.getComputedStyle(configTab).display : 'NOT FOUND');

                // Force switch to config tab
                switchTab('config');

                setTimeout(() => {
                    console.log('After switch - Config tab classes:', configTab ? configTab.className : 'NOT FOUND');
                    console.log('After switch - Config tab display style:', configTab ? window.getComputedStyle(configTab).display : 'NOT FOUND');
                }, 100);
            };

            // Load dashboard data on page load
            document.addEventListener('DOMContentLoaded', function() {
                console.log('DOM Content Loaded - initializing...');
                loadDashboardData();

                // Initialize configuration controls
                initializeConfigurationControls();

                // Add event listener for model selection changes
                const modelSelect = document.getElementById('model-select');
                if (modelSelect) {
                    modelSelect.addEventListener('change', function() {
                        const selectedModel = this.value;
                        if (selectedModel && selectedModel !== 'loading') {
                            saveSelectedModel(selectedModel);
                            console.log(`Model changed to: ${selectedModel}`);

                            // Show toast notification for model change
                            showModelChangeNotification(selectedModel);

                            // Auto-create new conversation when model changes
                            if (currentConversationId) {
                                createNewConversation();
                            }
                        }
                    });
                }

                // Log initial tab states for debugging
                console.log('Initial tab states:');
                document.querySelectorAll('.tab-content').forEach(tab => {
                    console.log(`Tab ${tab.id}: classes="${tab.className}", display="${window.getComputedStyle(tab).display}"`);
                });
            });
        </script>
    </body>
    </html>
    """


@flask_app.route('/api/status')
def api_status():
    """API endpoint for system status."""
    try:
        run_async(ensure_voxchat_initialized())
        
        performance_stats = voxchat_app.get_performance_stats()
        health_status = voxchat_app.get_health_status()
        
        return jsonify({
            'status': 'success',
            'data': {
                'health': health_status,
                'performance': performance_stats,
                'timestamp': datetime.now().isoformat()
            }
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@flask_app.route('/api/conversations')
def api_conversations():
    """API endpoint for conversations list."""
    try:
        run_async(ensure_voxchat_initialized())
        conversations = run_async(voxchat_app.list_conversations())

        # Convert conversations to dict format
        conversations_data = []
        for conv in conversations:
            conversations_data.append({
                'id': str(conv.id),
                'title': conv.title,
                'created_at': conv.created_at.isoformat(),
                'updated_at': conv.updated_at.isoformat(),
                'message_count': len(conv.messages),
                'total_tokens': conv.total_tokens,
                'model_name': conv.model_name
            })

        # Sort by updated_at descending (most recent first)
        conversations_data.sort(key=lambda x: x['updated_at'], reverse=True)

        return jsonify({
            'status': 'success',
            'data': conversations_data
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@flask_app.route('/api/conversations/<conversation_id>/switch', methods=['POST'])
def api_switch_conversation(conversation_id):
    """API endpoint to switch to a specific conversation."""
    async def switch_conversation_async():
        """Switch to the specified conversation."""
        await ensure_voxchat_initialized()
        conversation = await voxchat_app.switch_conversation(conversation_id)

        return {
            'status': 'success',
            'data': {
                'id': str(conversation.id),
                'title': conversation.title,
                'model_name': conversation.model_name,
                'message_count': len(conversation.messages),
                'messages': [
                    {
                        'id': str(msg.id),
                        'role': msg.role.value,
                        'content': msg.content,
                        'timestamp': msg.timestamp.isoformat(),
                        'token_count': msg.token_count
                    } for msg in conversation.messages
                ]
            }
        }

    try:
        result = run_async(switch_conversation_async())
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error switching conversation: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@flask_app.route('/api/conversations/new', methods=['POST'])
def api_new_conversation():
    """API endpoint to create a new conversation."""
    async def create_conversation_async():
        """Create a new conversation."""
        await ensure_voxchat_initialized()

        data = request.get_json() or {}
        title = data.get('title', 'New Conversation')
        model_name = data.get('model', 'anthropic/claude-3-haiku')

        conversation = await voxchat_app.create_conversation(title)
        if model_name != conversation.model_name:
            await voxchat_app.switch_model(model_name, conversation.id)
            conversation = voxchat_app.active_conversation

        return {
            'status': 'success',
            'data': {
                'id': str(conversation.id),
                'title': conversation.title,
                'model_name': conversation.model_name,
                'message_count': 0,
                'messages': []
            }
        }

    try:
        result = run_async(create_conversation_async())
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error creating conversation: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@flask_app.route('/api/models')
def api_models():
    """API endpoint for available models list."""
    async def fetch_models_async():
        """Fetch models in a single async function."""
        from voxchat.api.openrouter import OpenRouterClient

        # Create a fresh OpenRouter client for this request
        async with OpenRouterClient() as client:
            models = await client.list_models_async()

            # Format models for dropdown
            models_data = []
            for model in models:
                models_data.append({
                    'id': model.get('id', ''),
                    'name': model.get('name', model.get('id', 'Unknown')),
                    'description': model.get('description', ''),
                    'pricing': model.get('pricing', {}),
                    'context_length': model.get('context_length', 0)
                })

            # Sort by name for better UX
            models_data.sort(key=lambda x: x['name'])

            return {
                'status': 'success',
                'data': models_data
            }

    try:
        result = run_async(fetch_models_async())
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error fetching models: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@flask_app.route('/api/chat', methods=['POST'])
def api_chat():
    """API endpoint for sending chat messages and getting AI responses."""
    async def handle_chat_async():
        """Handle the entire chat process in a single async function."""
        # Ensure VoxChat is initialized
        await ensure_voxchat_initialized()

        data = request.get_json()
        if not data or 'message' not in data:
            return {
                'status': 'error',
                'message': 'Message content is required'
            }, 400

        message_content = data['message']
        conversation_id = data.get('conversation_id')
        model = data.get('model', 'anthropic/claude-3-haiku')

        try:
            # Send user message
            user_message = await voxchat_app.send_message(message_content, conversation_id)

            # Get the current conversation
            conversation = voxchat_app.active_conversation
            if not conversation:
                raise VoxChatError("No active conversation")

            # Switch model if different from current conversation model
            if model != conversation.model_name:
                await voxchat_app.switch_model(model, conversation.id)
                # Refresh conversation reference after model switch
                conversation = voxchat_app.active_conversation

            # Prepare messages for AI (include conversation history)
            messages = []
            for msg in conversation.messages:
                messages.append(msg)

            # Get AI response using a fresh OpenRouter client with current configuration
            from voxchat.api.openrouter import OpenRouterClient

            # Prepare API parameters using current configuration
            api_params = {
                'messages': messages,
                'model': model,
                'max_tokens': voxchat_app.config.models.max_tokens,
                'temperature': voxchat_app.config.models.temperature,
                'top_p': voxchat_app.config.models.top_p,
                'frequency_penalty': voxchat_app.config.models.frequency_penalty,
                'presence_penalty': voxchat_app.config.models.presence_penalty
            }

            # Remove parameters that are at their default values to avoid API issues
            if api_params['frequency_penalty'] == 0.0:
                del api_params['frequency_penalty']
            if api_params['presence_penalty'] == 0.0:
                del api_params['presence_penalty']

            async with OpenRouterClient() as client:
                ai_response = await client.chat_completion_async(**api_params)

            # Create AI message and add to conversation
            ai_message = Message(
                role=MessageRole.ASSISTANT,
                content=ai_response.content
            )
            ai_message.update_token_count(ai_response.usage.completion_tokens)

            # Add AI response to conversation
            await voxchat_app.conversation_manager.add_message(conversation.id, ai_message)

            return {
                'status': 'success',
                'data': {
                    'user_message': {
                        'id': str(user_message.id),
                        'content': user_message.content,
                        'token_count': user_message.token_count,
                        'timestamp': user_message.timestamp.isoformat()
                    },
                    'ai_response': {
                        'id': str(ai_message.id),
                        'content': ai_message.content,
                        'token_count': ai_message.token_count,
                        'timestamp': ai_message.timestamp.isoformat()
                    },
                    'conversation_id': str(conversation.id),
                    'model_used': model,
                    'total_tokens': ai_response.usage.total_tokens
                }
            }

        except Exception as ai_error:
            logger.error(f"AI response error: {ai_error}")
            return {
                'status': 'partial_success',
                'data': {
                    'user_message': {
                        'id': str(user_message.id),
                        'content': user_message.content,
                        'token_count': user_message.token_count,
                        'timestamp': user_message.timestamp.isoformat()
                    },
                    'ai_response': {
                        'content': f"Sorry, I encountered an error: {str(ai_error)}",
                        'error': True
                    },
                    'conversation_id': str(voxchat_app.active_conversation.id) if voxchat_app.active_conversation else None
                }
            }

    # Execute the async function and return the result
    try:
        result = run_async(handle_chat_async())
        if isinstance(result, tuple):
            return jsonify(result[0]), result[1]
        else:
            return jsonify(result)
    except Exception as e:
        logger.error(f"Chat API error: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


# Legacy routes for backward compatibility
@flask_app.route('/chat')
def chat_redirect():
    """Redirect to unified interface."""
    return redirect(url_for('unified_interface') + '#chat')

@flask_app.route('/config')
def config_redirect():
    """Redirect to unified interface."""
    return redirect(url_for('unified_interface') + '#config')


@flask_app.route('/api/config', methods=['GET'])
def get_configuration():
    """Get current model configuration."""
    try:
        config_data = {
            'temperature': voxchat_app.config.models.temperature,
            'top_p': voxchat_app.config.models.top_p,
            'max_tokens': voxchat_app.config.models.max_tokens,
            'frequency_penalty': voxchat_app.config.models.frequency_penalty,
            'presence_penalty': voxchat_app.config.models.presence_penalty,
            'stop_sequences': []  # Default empty, can be extended later
        }

        return jsonify({
            'status': 'success',
            'data': config_data
        })
    except Exception as e:
        logger.error(f"Error getting configuration: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@flask_app.route('/api/config', methods=['POST'])
def save_configuration():
    """Save model configuration."""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'status': 'error',
                'message': 'No configuration data provided'
            }), 400

        # Validate and update configuration
        if 'temperature' in data:
            temp = float(data['temperature'])
            if 0.0 <= temp <= 2.0:
                voxchat_app.config.models.temperature = temp
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'Temperature must be between 0.0 and 2.0'
                }), 400

        if 'top_p' in data:
            top_p = float(data['top_p'])
            if 0.0 <= top_p <= 1.0:
                voxchat_app.config.models.top_p = top_p
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'Top-p must be between 0.0 and 1.0'
                }), 400

        if 'max_tokens' in data:
            max_tokens = int(data['max_tokens'])
            if 1 <= max_tokens <= 128000:
                voxchat_app.config.models.max_tokens = max_tokens
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'Max tokens must be between 1 and 128000'
                }), 400

        if 'frequency_penalty' in data:
            freq_penalty = float(data['frequency_penalty'])
            if -2.0 <= freq_penalty <= 2.0:
                voxchat_app.config.models.frequency_penalty = freq_penalty
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'Frequency penalty must be between -2.0 and 2.0'
                }), 400

        if 'presence_penalty' in data:
            pres_penalty = float(data['presence_penalty'])
            if -2.0 <= pres_penalty <= 2.0:
                voxchat_app.config.models.presence_penalty = pres_penalty
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'Presence penalty must be between -2.0 and 2.0'
                }), 400

        # Note: stop_sequences can be handled in future updates

        # Save configuration to file (optional - for persistence)
        try:
            config_file = '.voxchat/user_config.json'
            os.makedirs(os.path.dirname(config_file), exist_ok=True)

            config_to_save = {
                'temperature': voxchat_app.config.models.temperature,
                'top_p': voxchat_app.config.models.top_p,
                'max_tokens': voxchat_app.config.models.max_tokens,
                'frequency_penalty': voxchat_app.config.models.frequency_penalty,
                'presence_penalty': voxchat_app.config.models.presence_penalty,
                'updated_at': datetime.now().isoformat()
            }

            with open(config_file, 'w') as f:
                json.dump(config_to_save, f, indent=2)

        except Exception as save_error:
            logger.warning(f"Could not save configuration to file: {save_error}")

        return jsonify({
            'status': 'success',
            'message': 'Configuration saved successfully',
            'data': {
                'temperature': voxchat_app.config.models.temperature,
                'top_p': voxchat_app.config.models.top_p,
                'max_tokens': voxchat_app.config.models.max_tokens,
                'frequency_penalty': voxchat_app.config.models.frequency_penalty,
                'presence_penalty': voxchat_app.config.models.presence_penalty
            }
        })

    except ValueError as e:
        return jsonify({
            'status': 'error',
            'message': f'Invalid parameter value: {str(e)}'
        }), 400
    except Exception as e:
        logger.error(f"Error saving configuration: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


if __name__ == '__main__':
    port = 8080
    print("🚀 Starting VoxChat Web Dashboard...")
    print(f"📊 Dashboard: http://localhost:{port}")
    print(f"💬 Chat Interface: http://localhost:{port}/chat")
    print(f"⚙️ Configuration: http://localhost:{port}/config")
    print("\nPress Ctrl+C to stop the server")

    flask_app.run(host='0.0.0.0', port=port, debug=True)
