"""
Enhanced conversation history management service for VoxChat.

This module provides comprehensive conversation management including:
- Advanced conversation storage and retrieval
- Search and filtering capabilities
- Archive and backup functionality
- Performance optimization with caching
- Async operations
- Conversation analytics and insights
"""

import asyncio
import json
import logging
import threading
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import List, Optional, Dict, Any, Set, Tuple
from dataclasses import dataclass, field
import hashlib
import shutil

from voxchat.core.models import Message, Conversation, MessageRole, FileAttachment
from voxchat.core.exceptions import ConversationNotFoundError, HistoryManagerError

logger = logging.getLogger(__name__)


class HistoryManager:
    """Service for managing conversation history."""

    def __init__(self, storage_dir: str = ".voxchat"):
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(exist_ok=True)
        self.conversations_file = self.storage_dir / "conversations.json"

        # Ensure conversations file exists
        if not self.conversations_file.exists():
            self._save_conversations({})

    def _load_conversations(self) -> Dict[str, Dict[str, Any]]:
        """Load conversations from storage."""
        try:
            with open(self.conversations_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data
        except (json.JSONDecodeError, FileNotFoundError) as e:
            logger.warning(f"Error loading conversations: {e}")
            return {}

    def _save_conversations(self, conversations: Dict[str, Dict[str, Any]]) -> None:
        """Save conversations to storage."""
        try:
            with open(self.conversations_file, 'w', encoding='utf-8') as f:
                json.dump(conversations, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error saving conversations: {e}")

    def _message_to_dict(self, message: Message) -> Dict[str, Any]:
        """Convert Message object to dictionary."""
        return {
            "role": message.role,
            "content": message.content,
            "timestamp": message.timestamp.isoformat(),
            "metadata": message.metadata or {}
        }

    def _dict_to_message(self, data: Dict[str, Any]) -> Message:
        """Convert dictionary to Message object."""
        return Message(
            role=data["role"],
            content=data["content"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            metadata=data.get("metadata", {})
        )

    def _conversation_to_dict(self, conversation: Conversation) -> Dict[str, Any]:
        """Convert Conversation object to dictionary."""
        return {
            "id": conversation.id,
            "title": conversation.title,
            "model_name": conversation.model_name,
            "total_tokens": conversation.total_tokens,
            "messages": [self._message_to_dict(msg) for msg in conversation.messages],
            "created_at": conversation.created_at.isoformat(),
            "updated_at": conversation.updated_at.isoformat(),
            "settings": conversation.settings or {},
            "metadata": conversation.metadata or {}
        }

    def _dict_to_conversation(self, data: Dict[str, Any]) -> Conversation:
        """Convert dictionary to Conversation object."""
        return Conversation(
            id=data["id"],
            title=data["title"],
            model_name=data.get("model_name", "anthropic/claude-3-haiku"),
            total_tokens=data.get("total_tokens", 0),
            messages=[self._dict_to_message(msg) for msg in data["messages"]],
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"]),
            settings=data.get("settings", {}),
            metadata=data.get("metadata", {})
        )

    def create_conversation(self, title: str = "New Conversation") -> Conversation:
        """Create a new conversation."""
        conversation_id = f"conv_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        conversation = Conversation(
            id=conversation_id,
            title=title,
            messages=[],
            created_at=datetime.now(),
            updated_at=datetime.now(),
            metadata={}
        )

        # Save to storage
        conversations = self._load_conversations()
        conversations[conversation_id] = self._conversation_to_dict(conversation)
        self._save_conversations(conversations)

        logger.info(f"Created new conversation: {conversation_id}")
        return conversation

    def get_conversation(self, conversation_id: str) -> Conversation:
        """Get a conversation by ID."""
        conversations = self._load_conversations()

        if conversation_id not in conversations:
            raise ConversationNotFoundError(f"Conversation {conversation_id} not found")

        return self._dict_to_conversation(conversations[conversation_id])

    def list_conversations(self) -> List[Conversation]:
        """List all conversations."""
        conversations = self._load_conversations()
        return [self._dict_to_conversation(data) for data in conversations.values()]

    def update_conversation(self, conversation: Conversation) -> None:
        """Update an existing conversation."""
        conversations = self._load_conversations()

        if conversation.id not in conversations:
            raise ConversationNotFoundError(f"Conversation {conversation.id} not found")

        conversation.updated_at = datetime.now()
        conversations[conversation.id] = self._conversation_to_dict(conversation)
        self._save_conversations(conversations)

        logger.info(f"Updated conversation: {conversation.id}")

    def delete_conversation(self, conversation_id: str) -> None:
        """Delete a conversation."""
        conversations = self._load_conversations()

        if conversation_id not in conversations:
            raise ConversationNotFoundError(f"Conversation {conversation_id} not found")

        del conversations[conversation_id]
        self._save_conversations(conversations)

        logger.info(f"Deleted conversation: {conversation_id}")

    def add_message(self, conversation_id: str, message: Message) -> None:
        """Add a message to a conversation."""
        conversation = self.get_conversation(conversation_id)
        conversation.messages.append(message)
        self.update_conversation(conversation)

    def clear_conversation(self, conversation_id: str) -> None:
        """Clear all messages from a conversation."""
        conversation = self.get_conversation(conversation_id)
        conversation.messages = []
        self.update_conversation(conversation)

        logger.info(f"Cleared conversation: {conversation_id}")

    def search_conversations(self, query: str) -> List[Conversation]:
        """Search conversations by title or content."""
        conversations = self.list_conversations()
        query_lower = query.lower()

        results = []
        for conv in conversations:
            if (query_lower in conv.title.lower() or
                any(query_lower in msg.content.lower() for msg in conv.messages)):
                results.append(conv)

        return results


class ConversationManager:
    """
    Enhanced conversation management service.

    Provides advanced features including:
    - Async operations for better performance
    - Conversation analytics and insights
    - Advanced search and filtering
    - Archive and backup functionality
    - Performance optimization with caching
    - Conversation templates and workflows
    """

    def __init__(self, history_manager: HistoryManager = None,
                 cache_size: int = 100,
                 max_workers: int = 4):
        self.history_manager = history_manager or HistoryManager()
        self.logger = logging.getLogger(__name__)

        # Thread pool for async operations
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

        # Cache for frequently accessed conversations
        self._conversation_cache: Dict[str, Conversation] = {}
        self._cache_size = cache_size
        self._cache_lock = threading.Lock()

        # Archive settings
        self.archive_dir = Path(".voxchat/archives")
        self.archive_dir.mkdir(parents=True, exist_ok=True)

        # Analytics data
        self._analytics = {
            'total_conversations': 0,
            'total_messages': 0,
            'total_tokens': 0,
            'most_active_day': None,
            'average_conversation_length': 0
        }

        self.logger.info("ConversationManager initialized")

    async def create_conversation(self, title: str = "New Conversation",
                                model_name: str = None,
                                settings: Dict[str, Any] = None) -> Conversation:
        """Create a new conversation with enhanced features."""
        try:
            conversation = self.history_manager.create_conversation(title)

            if model_name:
                conversation.model_name = model_name
            if settings:
                conversation.settings.update(settings)

            # Update conversation with new settings
            self.history_manager.update_conversation(conversation)

            # Cache the conversation
            self._cache_conversation(conversation)

            # Update analytics
            await self._update_analytics()

            self.logger.info(f"Created conversation: {conversation.id}")
            return conversation

        except Exception as e:
            self.logger.error(f"Error creating conversation: {e}")
            raise HistoryManagerError(f"Failed to create conversation: {e}")

    async def get_conversation(self, conversation_id: str) -> Conversation:
        """Get a conversation by ID with caching."""
        try:
            # Check cache first
            if conversation_id in self._conversation_cache:
                self.logger.debug(f"Conversation {conversation_id} found in cache")
                return self._conversation_cache[conversation_id]

            # Load from storage
            conversation = self.history_manager.get_conversation(conversation_id)

            # Cache it
            self._cache_conversation(conversation)

            return conversation

        except ConversationNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"Error getting conversation {conversation_id}: {e}")
            raise HistoryManagerError(f"Failed to get conversation: {e}")

    async def list_conversations(self, limit: int = None,
                               offset: int = 0,
                               sort_by: str = "updated_at",
                               reverse: bool = True) -> List[Conversation]:
        """List conversations with advanced filtering and sorting."""
        try:
            conversations = self.history_manager.list_conversations()

            # Sort conversations
            if sort_by == "updated_at":
                conversations.sort(key=lambda x: x.updated_at, reverse=reverse)
            elif sort_by == "created_at":
                conversations.sort(key=lambda x: x.created_at, reverse=reverse)
            elif sort_by == "title":
                conversations.sort(key=lambda x: x.title.lower(), reverse=reverse)
            elif sort_by == "message_count":
                conversations.sort(key=lambda x: len(x.messages), reverse=reverse)

            # Apply pagination
            if limit:
                start = offset * limit
                end = start + limit
                conversations = conversations[start:end]

            return conversations

        except Exception as e:
            self.logger.error(f"Error listing conversations: {e}")
            raise HistoryManagerError(f"Failed to list conversations: {e}")

    async def update_conversation(self, conversation: Conversation) -> None:
        """Update a conversation with caching."""
        try:
            self.history_manager.update_conversation(conversation)
            self._cache_conversation(conversation)
            await self._update_analytics()

            self.logger.info(f"Updated conversation: {conversation.id}")

        except Exception as e:
            self.logger.error(f"Error updating conversation {conversation.id}: {e}")
            raise HistoryManagerError(f"Failed to update conversation: {e}")

    async def delete_conversation(self, conversation_id: str) -> None:
        """Delete a conversation and remove from cache."""
        try:
            self.history_manager.delete_conversation(conversation_id)

            # Remove from cache
            with self._cache_lock:
                self._conversation_cache.pop(conversation_id, None)

            await self._update_analytics()

            self.logger.info(f"Deleted conversation: {conversation_id}")

        except Exception as e:
            self.logger.error(f"Error deleting conversation {conversation_id}: {e}")
            raise HistoryManagerError(f"Failed to delete conversation: {e}")

    async def add_message(self, conversation_id: str, message: Message) -> None:
        """Add a message to a conversation with enhanced features."""
        try:
            conversation = await self.get_conversation(conversation_id)
            self.history_manager.add_message(conversation_id, message)

            # Update cache
            conversation.add_message(message)
            self._cache_conversation(conversation)

            # Update analytics
            self._analytics['total_messages'] += 1
            self._analytics['total_tokens'] += message.token_count

            self.logger.info(f"Added message to conversation: {conversation_id}")

        except Exception as e:
            self.logger.error(f"Error adding message to conversation {conversation_id}: {e}")
            raise HistoryManagerError(f"Failed to add message: {e}")

    # Advanced Search and Filtering
    async def search_conversations_advanced(self,
                                          query: str = None,
                                          date_from: datetime = None,
                                          date_to: datetime = None,
                                          model_name: str = None,
                                          min_messages: int = None,
                                          max_messages: int = None,
                                          has_attachments: bool = None) -> List[Conversation]:
        """Advanced search with multiple filters."""
        try:
            conversations = await self.list_conversations()

            results = []
            for conv in conversations:
                # Text search
                if query and not self._matches_query(conv, query):
                    continue

                # Date filtering
                if date_from and conv.updated_at < date_from:
                    continue
                if date_to and conv.updated_at > date_to:
                    continue

                # Model filtering
                if model_name and conv.model_name != model_name:
                    continue

                # Message count filtering
                msg_count = len(conv.messages)
                if min_messages and msg_count < min_messages:
                    continue
                if max_messages and msg_count > max_messages:
                    continue

                # Attachment filtering
                if has_attachments is not None:
                    has_atts = any(len(msg.attachments) > 0 for msg in conv.messages)
                    if has_atts != has_attachments:
                        continue

                results.append(conv)

            return results

        except Exception as e:
            self.logger.error(f"Error in advanced search: {e}")
            raise HistoryManagerError(f"Advanced search failed: {e}")

    def _matches_query(self, conversation: Conversation, query: str) -> bool:
        """Check if conversation matches search query."""
        query_lower = query.lower()

        # Search in title
        if query_lower in conversation.title.lower():
            return True

        # Search in message content
        for message in conversation.messages:
            if query_lower in message.content.lower():
                return True

        # Search in attachment filenames
        for message in conversation.messages:
            for attachment in message.attachments:
                if query_lower in attachment.filename.lower():
                    return True

        return False

    # Archive and Backup
    async def archive_conversation(self, conversation_id: str,
                                 archive_name: str = None) -> Path:
        """Archive a conversation to a separate file."""
        try:
            conversation = await self.get_conversation(conversation_id)

            # Create archive filename
            if not archive_name:
                archive_name = f"conversation_{conversation_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            archive_path = self.archive_dir / f"{archive_name}.json"

            # Export conversation data
            archive_data = {
                'conversation': self.history_manager._conversation_to_dict(conversation),
                'archived_at': datetime.now().isoformat(),
                'archive_version': '1.0'
            }

            with open(archive_path, 'w', encoding='utf-8') as f:
                json.dump(archive_data, f, indent=2, default=str)

            self.logger.info(f"Archived conversation {conversation_id} to {archive_path}")
            return archive_path

        except Exception as e:
            self.logger.error(f"Error archiving conversation {conversation_id}: {e}")
            raise HistoryManagerError(f"Failed to archive conversation: {e}")

    async def restore_conversation(self, archive_path: Path) -> Conversation:
        """Restore a conversation from an archive."""
        try:
            with open(archive_path, 'r', encoding='utf-8') as f:
                archive_data = json.load(f)

            conversation_data = archive_data['conversation']
            conversation = self.history_manager._dict_to_conversation(conversation_data)

            # Save restored conversation
            self.history_manager.update_conversation(conversation)
            self._cache_conversation(conversation)

            self.logger.info(f"Restored conversation {conversation.id} from {archive_path}")
            return conversation

        except Exception as e:
            self.logger.error(f"Error restoring conversation from {archive_path}: {e}")
            raise HistoryManagerError(f"Failed to restore conversation: {e}")

    async def create_backup(self, backup_path: Path = None) -> Path:
        """Create a full backup of all conversations."""
        try:
            if not backup_path:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_path = Path(f"backup_conversations_{timestamp}.json")

            conversations = await self.list_conversations()

            backup_data = {
                'backup_created_at': datetime.now().isoformat(),
                'conversations_count': len(conversations),
                'conversations': [self.history_manager._conversation_to_dict(conv) for conv in conversations]
            }

            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, default=str)

            self.logger.info(f"Created backup with {len(conversations)} conversations at {backup_path}")
            return backup_path

        except Exception as e:
            self.logger.error(f"Error creating backup: {e}")
            raise HistoryManagerError(f"Failed to create backup: {e}")

    # Analytics and Insights
    async def get_analytics(self) -> Dict[str, Any]:
        """Get conversation analytics and insights."""
        await self._update_analytics()
        return self._analytics.copy()

    async def _update_analytics(self) -> None:
        """Update analytics data."""
        try:
            conversations = await self.list_conversations()

            self._analytics['total_conversations'] = len(conversations)

            total_messages = sum(len(conv.messages) for conv in conversations)
            self._analytics['total_messages'] = total_messages

            total_tokens = sum(conv.total_tokens for conv in conversations)
            self._analytics['total_tokens'] = total_tokens

            if conversations:
                self._analytics['average_conversation_length'] = total_messages / len(conversations)

            # Find most active day
            day_counts = {}
            for conv in conversations:
                day = conv.updated_at.date()
                day_counts[day] = day_counts.get(day, 0) + 1

            if day_counts:
                most_active_day = max(day_counts.items(), key=lambda x: x[1])
                self._analytics['most_active_day'] = most_active_day[0].isoformat()

        except Exception as e:
            self.logger.error(f"Error updating analytics: {e}")

    # Performance Optimization
    def _cache_conversation(self, conversation: Conversation) -> None:
        """Cache a conversation with LRU eviction."""
        with self._cache_lock:
            if len(self._conversation_cache) >= self._cache_size:
                # Remove oldest entry (simple FIFO)
                oldest_key = next(iter(self._conversation_cache))
                del self._conversation_cache[oldest_key]

            self._conversation_cache[conversation.id] = conversation

    def clear_cache(self) -> None:
        """Clear the conversation cache."""
        with self._cache_lock:
            self._conversation_cache.clear()
        self.logger.info("Conversation cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._cache_lock:
            return {
                'cache_size': len(self._conversation_cache),
                'max_cache_size': self._cache_size,
                'cached_conversation_ids': list(self._conversation_cache.keys())
            }

    # Cleanup and Maintenance
    async def cleanup_old_conversations(self, days_old: int = 90) -> Dict[str, Any]:
        """Clean up conversations older than specified days."""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)
            conversations = await self.list_conversations()

            to_archive = []
            for conv in conversations:
                if conv.updated_at < cutoff_date:
                    to_archive.append(conv.id)

            archived_count = 0
            for conv_id in to_archive:
                try:
                    await self.archive_conversation(conv_id)
                    await self.delete_conversation(conv_id)
                    archived_count += 1
                except Exception as e:
                    self.logger.warning(f"Failed to archive conversation {conv_id}: {e}")

            result = {
                'archived_count': archived_count,
                'total_processed': len(to_archive),
                'cutoff_date': cutoff_date.isoformat()
            }

            self.logger.info(f"Cleanup completed: {result}")
            return result

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
            raise HistoryManagerError(f"Cleanup failed: {e}")

    async def save_all_conversations(self) -> None:
        """Save all cached conversations to storage."""
        try:
            with self._cache_lock:
                for conversation in self._conversation_cache.values():
                    self.history_manager.update_conversation(conversation)

            self.logger.info("All conversations saved")

        except Exception as e:
            self.logger.error(f"Error saving conversations: {e}")
            raise HistoryManagerError(f"Failed to save conversations: {e}")

    async def close(self) -> None:
        """Cleanup resources."""
        try:
            # Save all cached conversations
            await self.save_all_conversations()

            # Shutdown thread pool
            self.executor.shutdown(wait=True)

            # Clear cache
            self.clear_cache()

            self.logger.info("ConversationManager closed")

        except Exception as e:
            self.logger.error(f"Error closing ConversationManager: {e}")