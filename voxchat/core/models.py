"""
Core data models for VoxChat.

This module defines the core data structures used throughout the VoxChat application,
including messages, conversations, file attachments, and model configurations.
All models use dataclasses for immutability and include comprehensive validation,
serialization methods, and business logic methods.
"""

import json
import uuid
from dataclasses import dataclass, field, asdict
from datetime import datetime
from typing import List, Dict, Any, Optional, Union
from enum import Enum


class MessageRole(str, Enum):
    """Enumeration of valid message roles."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class FileType(str, Enum):
    """Enumeration of supported file attachment types."""
    IMAGE = "image"
    DOCUMENT = "document"


@dataclass
class FileAttachment:
    """Represents a file attachment in a message."""
    id: uuid.UUID = field(default_factory=uuid.uuid4)
    filename: str = ""
    file_path: str = ""
    file_type: FileType = FileType.DOCUMENT
    mime_type: str = ""
    size_bytes: int = 0
    uploaded_at: datetime = field(default_factory=datetime.now)
    processed: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Validate file attachment after initialization."""
        if not self.filename:
            raise ValueError("Filename cannot be empty")
        if not self.file_path:
            raise ValueError("File path cannot be empty")
        if self.size_bytes < 0:
            raise ValueError("File size cannot be negative")
        if not self.mime_type:
            raise ValueError("MIME type cannot be empty")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        data = asdict(self)
        data['id'] = str(self.id)
        data['uploaded_at'] = self.uploaded_at.isoformat()
        data['file_type'] = self.file_type.value
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FileAttachment':
        """Create FileAttachment from dictionary."""
        data_copy = data.copy()
        data_copy['id'] = uuid.UUID(data_copy['id'])
        data_copy['uploaded_at'] = datetime.fromisoformat(data_copy['uploaded_at'])
        data_copy['file_type'] = FileType(data_copy['file_type'])
        return cls(**data_copy)

    def get_file_extension(self) -> str:
        """Get file extension from filename."""
        return self.filename.split('.')[-1].lower() if '.' in self.filename else ""

    def is_image(self) -> bool:
        """Check if attachment is an image."""
        return self.file_type == FileType.IMAGE

    def is_document(self) -> bool:
        """Check if attachment is a document."""
        return self.file_type == FileType.DOCUMENT


@dataclass
class ModelConfiguration:
    """Configuration for AI model providers."""
    provider: str = ""
    model_name: str = ""
    context_length: int = 4096
    input_token_cost: float = 0.0  # Cost per input token
    output_token_cost: float = 0.0  # Cost per output token

    def __post_init__(self):
        """Validate model configuration after initialization."""
        if not self.provider:
            raise ValueError("Provider cannot be empty")
        if not self.model_name:
            raise ValueError("Model name cannot be empty")
        if self.context_length <= 0:
            raise ValueError("Context length must be positive")
        if self.input_token_cost < 0:
            raise ValueError("Input token cost cannot be negative")
        if self.output_token_cost < 0:
            raise ValueError("Output token cost cannot be negative")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModelConfiguration':
        """Create ModelConfiguration from dictionary."""
        return cls(**data)

    def calculate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """Calculate total cost for token usage."""
        return (input_tokens * self.input_token_cost) + (output_tokens * self.output_token_cost)

    def is_within_context_limit(self, token_count: int) -> bool:
        """Check if token count is within model's context limit."""
        return token_count <= self.context_length


@dataclass
class Message:
    """Represents a chat message with attachments and metadata."""
    id: uuid.UUID = field(default_factory=uuid.uuid4)
    role: MessageRole = MessageRole.USER
    content: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    token_count: int = 0
    attachments: List[FileAttachment] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Validate message after initialization."""
        if not isinstance(self.role, MessageRole):
            self.role = MessageRole(self.role)
        if self.token_count < 0:
            raise ValueError("Token count cannot be negative")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        data = asdict(self)
        data['id'] = str(self.id)
        data['role'] = self.role.value
        data['timestamp'] = self.timestamp.isoformat()
        data['attachments'] = [attachment.to_dict() for attachment in self.attachments]
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """Create Message from dictionary."""
        data_copy = data.copy()
        data_copy['id'] = uuid.UUID(data_copy['id'])
        data_copy['role'] = MessageRole(data_copy['role'])
        data_copy['timestamp'] = datetime.fromisoformat(data_copy['timestamp'])
        data_copy['attachments'] = [FileAttachment.from_dict(att) for att in data_copy['attachments']]
        return cls(**data_copy)

    def add_attachment(self, attachment: FileAttachment) -> None:
        """Add a file attachment to the message."""
        self.attachments.append(attachment)

    def remove_attachment(self, attachment_id: uuid.UUID) -> bool:
        """Remove attachment by ID. Returns True if removed."""
        for i, attachment in enumerate(self.attachments):
            if attachment.id == attachment_id:
                self.attachments.pop(i)
                return True
        return False

    def get_total_attachment_size(self) -> int:
        """Get total size of all attachments in bytes."""
        return sum(attachment.size_bytes for attachment in self.attachments)

    def has_attachments(self) -> bool:
        """Check if message has any attachments."""
        return len(self.attachments) > 0

    def get_attachment_by_type(self, file_type: FileType) -> List[FileAttachment]:
        """Get attachments filtered by file type."""
        return [att for att in self.attachments if att.file_type == file_type]

    def update_token_count(self, new_count: int) -> None:
        """Update the token count for this message."""
        if new_count < 0:
            raise ValueError("Token count cannot be negative")
        self.token_count = new_count


@dataclass
class Conversation:
    """Represents a conversation session with messages and settings."""
    id: uuid.UUID = field(default_factory=uuid.uuid4)
    title: str = "New Conversation"
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    messages: List[Message] = field(default_factory=list)
    model_name: str = "anthropic/claude-3-haiku"
    total_tokens: int = 0
    settings: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Validate conversation after initialization."""
        if not self.title.strip():
            self.title = "New Conversation"
        if self.total_tokens < 0:
            raise ValueError("Total tokens cannot be negative")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        data = asdict(self)
        data['id'] = str(self.id)
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        data['messages'] = [message.to_dict() for message in self.messages]
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Conversation':
        """Create Conversation from dictionary."""
        data_copy = data.copy()
        data_copy['id'] = uuid.UUID(data_copy['id'])
        data_copy['created_at'] = datetime.fromisoformat(data_copy['created_at'])
        data_copy['updated_at'] = datetime.fromisoformat(data_copy['updated_at'])
        data_copy['messages'] = [Message.from_dict(msg) for msg in data_copy['messages']]
        return cls(**data_copy)

    def add_message(self, message: Message) -> None:
        """Add a message to the conversation."""
        self.messages.append(message)
        self.updated_at = datetime.now()
        self._update_total_tokens()

    def remove_message(self, message_id: uuid.UUID) -> bool:
        """Remove message by ID. Returns True if removed."""
        for i, message in enumerate(self.messages):
            if message.id == message_id:
                self.messages.pop(i)
                self.updated_at = datetime.now()
                self._update_total_tokens()
                return True
        return False

    def get_messages_by_role(self, role: MessageRole) -> List[Message]:
        """Get messages filtered by role."""
        return [msg for msg in self.messages if msg.role == role]

    def get_user_messages(self) -> List[Message]:
        """Get all user messages."""
        return self.get_messages_by_role(MessageRole.USER)

    def get_assistant_messages(self) -> List[Message]:
        """Get all assistant messages."""
        return self.get_messages_by_role(MessageRole.ASSISTANT)

    def get_system_messages(self) -> List[Message]:
        """Get all system messages."""
        return self.get_messages_by_role(MessageRole.SYSTEM)

    def get_last_message(self) -> Optional[Message]:
        """Get the last message in the conversation."""
        return self.messages[-1] if self.messages else None

    def get_total_attachments(self) -> int:
        """Get total number of attachments across all messages."""
        return sum(len(msg.attachments) for msg in self.messages)

    def get_all_attachments(self) -> List[FileAttachment]:
        """Get all attachments from all messages."""
        all_attachments = []
        for message in self.messages:
            all_attachments.extend(message.attachments)
        return all_attachments

    def _update_total_tokens(self) -> None:
        """Update total token count for the conversation."""
        self.total_tokens = sum(msg.token_count for msg in self.messages)

    def generate_summary(self, max_length: int = 200) -> str:
        """Generate a summary of the conversation."""
        if not self.messages:
            return "Empty conversation"

        # Get first user message as starting point
        user_messages = self.get_user_messages()
        if not user_messages:
            return self.title

        first_message = user_messages[0].content[:100] + "..." if len(user_messages[0].content) > 100 else user_messages[0].content

        # Count messages by type
        total_messages = len(self.messages)
        user_count = len(user_messages)
        assistant_count = len(self.get_assistant_messages())
        attachment_count = self.get_total_attachments()

        summary_parts = [f"Started with: {first_message}"]
        summary_parts.append(f"Total messages: {total_messages} ({user_count} user, {assistant_count} assistant)")
        if attachment_count > 0:
            summary_parts.append(f"Attachments: {attachment_count}")
        summary_parts.append(f"Total tokens: {self.total_tokens}")

        full_summary = " | ".join(summary_parts)
        return full_summary[:max_length] + "..." if len(full_summary) > max_length else full_summary

    def update_settings(self, new_settings: Dict[str, Any]) -> None:
        """Update conversation settings."""
        self.settings.update(new_settings)
        self.updated_at = datetime.now()

    def get_setting(self, key: str, default: Any = None) -> Any:
        """Get a specific setting value."""
        return self.settings.get(key, default)


# Keep existing TokenUsage and APIResponse for backward compatibility
@dataclass
class TokenUsage:
    """Represents token usage for API calls."""
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TokenUsage':
        """Create TokenUsage from dictionary."""
        return cls(**data)


@dataclass
class APIResponse:
    """Represents a response from an AI API."""
    content: str
    model: str
    usage: TokenUsage
    finish_reason: str
    metadata: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        data = asdict(self)
        data['usage'] = self.usage.to_dict()
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'APIResponse':
        """Create APIResponse from dictionary."""
        data_copy = data.copy()
        data_copy['usage'] = TokenUsage.from_dict(data_copy['usage'])
        return cls(**data_copy)